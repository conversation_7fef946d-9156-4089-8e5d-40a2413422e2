@echo off
echo ========================================
echo    DCST Tool - Cleanup and Build Script
echo ========================================
echo.

echo 🧹 Pulizia cache Python e file di log...
for /d /r . %%d in (__pycache__) do @if exist "%%d" rd /s /q "%%d"
del /s /q *.pyc 2>nul
del /s /q *.log 2>nul

echo 🧹 Rimozione build artifacts...
if exist "build" rd /s /q "build"
if exist "dist" rd /s /q "dist"
del /q *.spec 2>nul

echo 🧹 Pulizia log files dal Desktop...
del "%USERPROFILE%\Desktop\dcst_app.log" 2>nul
del "%USERPROFILE%\Desktop\*.log" 2>nul

echo.
echo 📦 Verifica dipendenze...
pip install networkx matplotlib pandas tabulate numpy tqdm Pillow memory-profiler psutil pyinstaller

echo.
echo 🧪 Test import moduli...
python -c "from app.gui import App; from app.algorithms import test_instance; print('✅ Import test passed')"
if %errorlevel% neq 0 (
    echo ❌ Test import fallito!
    pause
    exit /b 1
)

echo.
echo 🔨 Creazione eseguibile con PyInstaller...
pyinstaller dcst_tool.spec --clean --noconfirm

if %errorlevel% neq 0 (
    echo ❌ Build fallito!
    pause
    exit /b 1
)

echo.
echo 📋 Copia eseguibile sul Desktop...
copy "dist\DCST_Tool.exe" "%USERPROFILE%\Desktop\DCST_Tool.exe"

echo.
echo ✅ Build completato con successo!
echo 📁 Eseguibile disponibile in: %USERPROFILE%\Desktop\DCST_Tool.exe
echo 📁 Dimensione file: 
dir "%USERPROFILE%\Desktop\DCST_Tool.exe" | find "DCST_Tool.exe"

echo.
echo 🎯 Per testare l'applicazione:
echo    1. Esegui DCST_Tool.exe dal Desktop
echo    2. Verifica che tutti i moduli si carichino correttamente
echo    3. Testa le funzionalità principali
echo.

pause
