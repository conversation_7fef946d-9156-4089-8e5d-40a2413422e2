@echo off
title DCST Tool
echo ========================================
echo           DCST Tool
echo ========================================
echo.

REM Cambia alla directory del progetto
cd /d "%~dp0"

REM Verifica se Python è disponibile
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python non trovato!
    echo.
    echo Installare Python 3.12+ da: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python trovato
echo.

echo 🚀 Avvio DCST Tool...
echo.

REM Avvia l'applicazione direttamente
python run.py

REM Se c'è un errore, mostra il messaggio
if %errorlevel% neq 0 (
    echo.
    echo ❌ Errore nell'avvio dell'applicazione!
    echo.
    echo 🔧 Modalità debug disponibile:
    echo    python run_debug.py
    echo.
    pause
) else (
    echo.
    echo ✅ Applicazione chiusa correttamente.
)
