@echo off
echo ========================================
echo    Creazione App Portatile DCST Tool
echo ========================================
echo.

REM Crea la directory per l'app portatile
set "PORTABLE_DIR=%USERPROFILE%\Desktop\DCST_Tool_Portable"
if exist "%PORTABLE_DIR%" rd /s /q "%PORTABLE_DIR%"
mkdir "%PORTABLE_DIR%"

echo 📁 Creazione directory: %PORTABLE_DIR%

REM Copia tutti i file necessari
echo 📋 Copia file applicazione...
xcopy /E /I /Y "app" "%PORTABLE_DIR%\app\"
copy /Y "run.py" "%PORTABLE_DIR%\"
copy /Y "run_debug.py" "%PORTABLE_DIR%\"
copy /Y "icon.ico" "%PORTABLE_DIR%\"
copy /Y "README.md" "%PORTABLE_DIR%\"

REM Crea il launcher principale
echo 📝 Creazione launcher...
(
echo @echo off
echo title DCST Tool - Portable
echo cd /d "%%~dp0"
echo.
echo echo ========================================
echo echo           DCST Tool Portable
echo echo ========================================
echo echo.
echo.
echo REM Verifica Python
echo python --version ^>nul 2^>^&1
echo if %%errorlevel%% neq 0 ^(
echo     echo ❌ Python non trovato!
echo     echo.
echo     echo Questo programma richiede Python 3.12 o superiore.
echo     echo Scarica da: https://www.python.org/downloads/
echo     echo.
echo     pause
echo     exit /b 1
echo ^)
echo.
echo echo ✅ Python trovato
echo echo.
echo.
echo REM Installa dipendenze se necessario
echo echo 📦 Verifica dipendenze...
echo python -c "import tkinter, matplotlib, networkx, pandas, numpy, PIL; print('✅ Dipendenze OK'^)" 2^>nul
echo if %%errorlevel%% neq 0 ^(
echo     echo ⚠️  Installazione dipendenze in corso...
echo     pip install networkx matplotlib pandas tabulate numpy tqdm Pillow memory-profiler psutil
echo     if %%errorlevel%% neq 0 ^(
echo         echo ❌ Errore installazione dipendenze!
echo         pause
echo         exit /b 1
echo     ^)
echo     echo ✅ Dipendenze installate
echo ^)
echo.
echo echo.
echo echo 🚀 Avvio DCST Tool...
echo echo.
echo python run.py
echo.
echo if %%errorlevel%% neq 0 ^(
echo     echo.
echo     echo ❌ Errore nell'avvio! Prova la modalità debug:
echo     echo python run_debug.py
echo     echo.
echo     pause
echo ^)
) > "%PORTABLE_DIR%\DCST_Tool.cmd"

REM Crea un launcher debug
(
echo @echo off
echo title DCST Tool - Debug Mode
echo cd /d "%%~dp0"
echo echo 🔧 Modalità Debug DCST Tool
echo echo ========================================
echo python run_debug.py
echo pause
) > "%PORTABLE_DIR%\DCST_Tool_Debug.cmd"

REM Crea file README per l'app portatile
(
echo # DCST Tool - Versione Portatile
echo.
echo ## Come usare:
echo 1. Assicurati di avere Python 3.12+ installato
echo 2. Fai doppio clic su "DCST_Tool.cmd"
echo 3. L'applicazione installerà automaticamente le dipendenze se necessario
echo.
echo ## File inclusi:
echo - DCST_Tool.cmd          : Launcher principale
echo - DCST_Tool_Debug.cmd    : Launcher debug
echo - app/                   : Codice sorgente applicazione
echo - run.py                 : Script di avvio
echo - run_debug.py           : Script debug
echo - icon.ico               : Icona applicazione
echo.
echo ## Risoluzione problemi:
echo - Se l'app non si avvia, usa "DCST_Tool_Debug.cmd"
echo - Verifica che Python sia installato e nel PATH
echo - Controlla che tutte le dipendenze siano installate
echo.
echo ## Dipendenze richieste:
echo - networkx
echo - matplotlib  
echo - pandas
echo - tabulate
echo - numpy
echo - tqdm
echo - Pillow
echo - memory-profiler
echo - psutil
) > "%PORTABLE_DIR%\README_PORTABLE.md"

echo.
echo ✅ App portatile creata con successo!
echo 📁 Posizione: %PORTABLE_DIR%
echo.
echo 🎯 Per usare l'app:
echo    1. Vai in %PORTABLE_DIR%
echo    2. Fai doppio clic su "DCST_Tool.cmd"
echo.

REM Apri la cartella
explorer "%PORTABLE_DIR%"

pause
