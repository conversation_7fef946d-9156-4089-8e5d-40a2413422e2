@echo off
title DCST Tool
echo ========================================
echo           DCST Tool Launcher
echo ========================================
echo.

REM Cambia alla directory del progetto
cd /d "%~dp0"

REM Verifica se Python è disponibile
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python non trovato! Installare Python 3.12 o superiore.
    echo.
    echo Scarica Python da: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python trovato
echo.

REM Verifica dipendenze critiche
echo 📦 Verifica dipendenze...
python -c "import tkinter, matplotlib, networkx, pandas, numpy, PIL; print('✅ Dipendenze OK')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Dipendenze mancanti! Installazione in corso...
    pip install networkx matplotlib pandas tabulate numpy tqdm Pillow memory-profiler psutil
    if %errorlevel% neq 0 (
        echo ❌ Errore nell'installazione delle dipendenze!
        pause
        exit /b 1
    )
)

echo.
echo 🚀 Avvio DCST Tool...
echo.

REM Avvia l'applicazione
python run.py

REM Se c'è un errore, mostra il messaggio
if %errorlevel% neq 0 (
    echo.
    echo ❌ Errore nell'avvio dell'applicazione!
    echo.
    echo 🔧 Prova la modalità debug:
    echo    python run_debug.py
    echo.
    pause
)

echo.
echo ✅ Applicazione chiusa correttamente.
