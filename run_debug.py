# run_debug.py - Versione debug per PyInstaller

import os
import sys
import traceback

def main():
    try:
        print("🚀 Avvio DCST Tool (Debug Mode)")
        print(f"Python version: {sys.version}")
        print(f"Current directory: {os.getcwd()}")
        print(f"Executable: {sys.executable}")
        print(f"Script path: {__file__}")
        print("=" * 60)
        
        # Aggiungi il percorso corrente al sys.path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
            print(f"Added to sys.path: {current_dir}")
        
        print("📦 Testing imports...")
        
        # Test import base
        print("  - Testing tkinter...")
        import tkinter as tk
        from tkinter import ttk
        print("  ✅ tkinter OK")
        
        print("  - Testing matplotlib...")
        import matplotlib
        matplotlib.use('TkAgg')  # Forza backend TkAgg
        import matplotlib.pyplot as plt
        print("  ✅ matplotlib OK")
        
        print("  - Testing other dependencies...")
        import networkx as nx
        import pandas as pd
        import numpy as np
        from PIL import Image, ImageTk
        print("  ✅ Dependencies OK")
        
        print("  - Testing app modules...")
        from app.gui import App
        from app.algorithms import test_instance
        print("  ✅ App modules OK")
        
        print("\n🎯 Creating GUI...")
        
        # Crea la finestra principale
        root = tk.Tk()
        root.title("DCST Tool (Debug)")
        
        # Crea la progress bar
        progress_bar = ttk.Progressbar(root, orient="horizontal", length=300, mode="determinate")
        
        # Crea l'app
        app = App(root, progress_bar)
        
        print("✅ GUI created successfully!")
        print("🔄 Starting mainloop...")
        
        # Avvia l'app
        root.mainloop()
        
        print("✅ App closed normally")
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        traceback.print_exc()
        input("Press Enter to exit...")
    except Exception as e:
        print(f"❌ Error: {e}")
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
